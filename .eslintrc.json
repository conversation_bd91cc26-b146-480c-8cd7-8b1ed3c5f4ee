{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["tsconfig.json"], "createDefaultProgram": true}, "extends": ["plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates", "plugin:prettier/recommended"], "plugins": ["unused-imports", "simple-import-sort"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "app", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}], "@angular-eslint/no-empty-lifecycle-method": "off", "@angular-eslint/component-class-suffix": "off", "unused-imports/no-unused-imports": "error", "simple-import-sort/imports": "error", "simple-import-sort/exports": "error", "prettier/prettier": ["error", {"endOfLine": "auto"}]}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended"], "rules": {}}]}