$max: 30;
$offset: 5;
$unit: "px"; // Feel free to change the unit.

@mixin list-loop($className, $styleName) {
  $i: 0;
  @while $i <= $max {
    #{$className + $i} {
      #{$styleName}: #{$i + $unit} !important;
    }
    $i: $i + $offset;
  }
}

// Margins
@include list-loop(".m-t-", "margin-top");
@include list-loop(".m-", "margin");
@include list-loop(".m-b-", "margin-bottom");
@include list-loop(".ltr .m-l-", "margin-left");
@include list-loop(".ltr .m-r-", "margin-right");
@include list-loop(".rtl .m-l-", "margin-right");
@include list-loop(".rtl .m-r-", "margin-left");

// Paddings
@include list-loop(".p-t-", "padding-top");
@include list-loop(".p-", "padding");
@include list-loop(".p-b-", "padding-bottom");
@include list-loop(".ltr .p-l-", "padding-left");
@include list-loop(".ltr .p-r-", "padding-right");
@include list-loop(".rtl .p-r-", "padding-left");
@include list-loop(".rtl .p-l-", "padding-right");
