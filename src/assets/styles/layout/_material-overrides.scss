// Material Design Component Overrides
// This file contains custom styles to override Angular Material defaults

// Sidenav Container Background
.mat-drawer-container,
.mat-sidenav-container {
  background-color: #fafafa !important;
}
.mat-drawer {
    background-color: #fff !important;
    color: #000000de !important;
    box-shadow: 1px 0 20px #00000014 !important;
}

// Sidebar specific styling
.leftsidebar {
    background-color: #fff !important;
    border-right: 1px solid rgba(0,0,0,0.08) !important;
}

// Navigation list styling
.mat-nav-list {
    padding-top: 0 !important;

    .mat-list-item {
        height: auto !important;
        min-height: 48px !important;

        .mat-list-item-content {
            padding: 12px 16px !important;
            border-radius: 8px !important;
            margin: 2px 8px !important;
            transition: all 0.2s ease !important;
        }

        &:hover .mat-list-item-content {
            background-color: rgba(0,0,0,0.04) !important;
        }
    }
}

// Sidebar container styling
.mat-sidenav-content {
    background-color: #fafafa !important;
}

// Ensure proper sidebar positioning
.mat-drawer-container {
    position: relative !important;

    .mat-drawer {
        position: relative !important;
        transform: none !important;
        visibility: visible !important;
    }
}
// Menu Panel Background
.mat-mdc-menu-panel {
  background-color: #fff !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

// Additional Material overrides can be added here
.mat-toolbar {
  &.mat-primary {
    background-color: #fff !important;
    color: #333 !important;
  }
}

// Menu items
.mat-mdc-menu-item {
  &:hover {
    background-color: rgba(0,0,0,0.04) !important;
  }
}

// Dark theme overrides
.darkTheme {
  .mat-drawer-container,
  .mat-sidenav-container {
    background-color: #303030 !important;
  }

  .mat-mdc-menu-panel {
    background-color: #424242 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
  }

  .mat-toolbar {
    &.mat-primary {
      background-color: #424242 !important;
      color: #fff !important;
    }
  }

  .mat-mdc-menu-item {
    &:hover {
      background-color: rgba(255,255,255,0.04) !important;
    }
  }
}
