// Material Design Component Overrides
// This file contains custom styles to override Angular Material defaults

// Sidenav Container Background
.mat-drawer-container,
.mat-sidenav-container {
  background-color: #fafafa !important;
}
.mat-drawer {
    background-color: #fff !important;
    color: #000000de !important;
    box-shadow: 1px 0 20px #00000014 !important;
}
// Menu Panel Background
.mat-mdc-menu-panel {
  background-color: #fff !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}
.main-container .mat-mdc-card.mdc-card{
    background: #fff !important;
    border-radius: 20px !important;
    box-shadow: 1px 0 20px #00000014 !important;
    padding: 0;
    margin: 15px !important;
    overflow: hidden;
}
// Additional Material overrides can be added here
.mat-toolbar {
  &.mat-primary {
    background-color: #fff !important;
    color: #333 !important;
  }
}

// Menu items
.mat-mdc-menu-item {
  &:hover {
    background-color: rgba(0,0,0,0.04) !important;
  }
}

// Dark theme overrides
.darkTheme {
  .mat-drawer-container,
  .mat-sidenav-container {
    background-color: #303030 !important;
  }

  .mat-mdc-menu-panel {
    background-color: #424242 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
  }

  .mat-toolbar {
    &.mat-primary {
      background-color: #424242 !important;
      color: #fff !important;
    }
  }

  .mat-mdc-menu-item {
    &:hover {
      background-color: rgba(255,255,255,0.04) !important;
    }
  }
}
