@use "@angular/material" as mat;
@import "../variables"; // ✅ ensures all color and spacing vars are available

.horizontal {
  .page-wrapper {
    margin-left: 0 !important;
  }
}

.leftsidebar {
  width: $sidebarwidth;
  box-shadow: $box-shadow;
  border: 0;
  background-color: #fff !important;

  // Ensure full height
  height: 100vh !important;
  overflow-y: auto !important;

  // Custom scrollbar styling
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,0.2);
  }
}

.vsidebar,
.hsidebar {
  padding: 0;
  height: 100%;

  // Logo section styling
  .p-15 {
    padding: 20px 16px !important;
    border-bottom: 1px solid rgba(0,0,0,0.08);
    margin-bottom: 8px;
  }

  // Menu list styling
  .vmenu-list-item {
    padding: 0 8px;
  }

  .routeIcon {
    width: $sidebarIconSize;
    height: $sidebarIconSize;
    margin-right: 12px;
    color: #666;
  }

  .menu-list-item {
    margin: 0 0 4px 0;

    .mat-list-item-content {
      border-radius: 8px;
      padding: 12px 16px;
      transition: all 0.2s ease;
      min-height: 48px;
      display: flex;
      align-items: center;
    }

    &:hover {
      background-color: transparent;

      .mat-list-item-content {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    // Menu text styling
    .hide-menu {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }
}

.minisidebar {
  .leftsidebar {
    width: $minisidebar;
  }
}

// ✅ Active menu item styles
.vsidebar .menu-list-item.activeMenu,
.hsidebar .menu-list-item.activeMenu {
  .mat-list-item-content,
  &:hover .mat-list-item-content {
    background-color: #03c9d7 !important;
    color: #fff !important;
    box-shadow: 0 2px 4px rgba(3, 201, 215, 0.2) !important;

    .routeIcon {
      color: #fff !important;
    }

    .hide-menu {
      color: #fff !important;
      font-weight: 600 !important;
    }
  }
}
