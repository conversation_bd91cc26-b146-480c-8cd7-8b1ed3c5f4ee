@use "@angular/material" as mat;
@import "../variables"; // ✅ ensures all color and spacing vars are available

.horizontal {
  .page-wrapper {
    margin-left: 0 !important;
  }
}

.leftsidebar {
  width: $sidebarwidth;
  box-shadow: $box-shadow;
  border: 0;
}

.vsidebar,
.hsidebar {
  padding: 0 15px;

  .routeIcon {
    width: $sidebarIconSize;
    height: $sidebarIconSize;
    margin-right: 10px;
  }

  .menu-list-item {
    margin: 0 0 5px 0;

    .mat-list-item-content {
      border-radius: 6px;
      padding: 0 6px 0 10px;
      transition: background-color 0.2s ease;
    }

    &:hover {
      background-color: transparent;

      .mat-list-item-content {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
  }
}

.minisidebar {
  .leftsidebar {
    width: $minisidebar;
  }
}

// ✅ Active menu item styles
.vsidebar .menu-list-item.activeMenu,
.hsidebar .menu-list-item.activeMenu {
  .mat-list-item-content,
  &:hover .mat-list-item-content {
    color: $white;

    // ✅ Fallback: use Material accent if available, else custom color
    background-color:$accent-color;
  }
}
