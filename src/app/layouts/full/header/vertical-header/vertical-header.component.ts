import { Component, Input, OnInit } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { Router } from '@angular/router';

import { Messages } from '../../data/messages';
import { Notifications } from '../../data/notification';
import { Profile } from '../../data/profile';
import { AuthService } from './../../../../services/auth.service';

@Component({
  selector: 'app-vertical-header',
  templateUrl: './vertical-header.component.html',
  styleUrls: [],
  standalone: false
})
export class VerticalHeaderComponent implements OnInit {
  @Input() sidebartoggle: MatSidenav | any;

  public showSearch = false;
  public notifications: any[] = Notifications;
  public messages: any[] = Messages;
  public profiles: any[] = Profile;

  constructor(public authService: AuthService, private router: Router) {}

  ngOnInit(): void {}

  logout() {
    this.authService.signOut();
    this.router.navigate(['login']);
  }
}
