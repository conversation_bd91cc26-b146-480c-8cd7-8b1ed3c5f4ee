import { DatePipe } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { NgScrollbarModule } from 'ngx-scrollbar';

import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import { SpinnerModule } from './components/spinner/spinner.module';
import { AuthInterceptor } from './guard/auth.interceptor';
import { BlankComponent } from './layouts/blank/blank.component';
import { CustomizerComponent } from './layouts/full/customizer/customizer.component';
/**Components**/
import { FullComponent } from './layouts/full/full.component';
import { HorizontalHeaderComponent } from './layouts/full/header/horizontal-header/horizontal-header.component';
import { VerticalHeaderComponent } from './layouts/full/header/vertical-header/vertical-header.component';
import { LogoComponent } from './layouts/full/logo/logo.component';
import { HorizontalSidebarComponent } from './layouts/full/sidebar/horizontal-sidebar/horizontal-sidebar.component';
import { Hnavbar } from './layouts/full/sidebar/horizontal-sidebar/navbar/hnavbar.component';
import { MenuListItemComponent } from './layouts/full/sidebar/vertical-sidebar/menu-list-item/menu-list-item.component';
import { VerticalSidebarComponent } from './layouts/full/sidebar/vertical-sidebar/vertical-sidebar.component';
import { LoginComponent } from './login/login.component';
import { MaterialModule } from './material/material.module';
import { AuthService } from './services/auth.service';
import { CustomizerService } from './services/customizer.service';
//import { NavService } from './services/nav.service';

// Scrollbar configuration is now handled by ngx-scrollbar
@NgModule({
  declarations: [
    AppComponent,
    FullComponent,
    BlankComponent,
    VerticalHeaderComponent,
    HorizontalHeaderComponent,
    VerticalSidebarComponent,
    Hnavbar,
    MenuListItemComponent,
    HorizontalSidebarComponent,
    CustomizerComponent,
    LogoComponent,
    LoginComponent,
  ],
  imports: [
    SpinnerModule,
    BrowserModule,
    AppRoutingModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FeatherModule.pick(allIcons),
    FlexLayoutModule,
    BrowserAnimationsModule,
    HttpClientModule,
    NgScrollbarModule,
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    CustomizerService,
    DatePipe,
    AuthService,
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
