import { Routes } from '@angular/router';

import { ErrorComponent } from './error/error.component';

export const PagesRoutes: Routes = [
  {
    path: '',
    children: [
      {
        path: 'error',
        component: ErrorComponent,
      },
      {
        path: 'home',
        loadChildren: () => import('./home/<USER>').then((m) => m.HomeModule),
      },
      {
        path: '',
        redirectTo: 'home',
        pathMatch: 'full',
      },
      {
        path: 'profile',
        loadChildren: () => import('./user-profile/user-profile.module').then((m) => m.UserProfileModule),
      },
      {
        path: 'manage-Admins',
        loadChildren: () => import('./manage-admin/manage-admin.module').then((m) => m.ManageAdminModule),
      },
    ],
  },
];
