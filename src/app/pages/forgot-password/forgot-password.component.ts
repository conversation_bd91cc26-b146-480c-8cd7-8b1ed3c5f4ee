import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';
import { ChurchService } from 'src/app/services/church.service';
import { CustomizerService } from 'src/app/services/customizer.service';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss'],
  standalone: false
})
export class ForgotPasswordComponent implements OnInit {
  msg = '';

  loading: boolean = false;
  email = new UntypedFormControl('', [Validators.required, Validators.email]);

  constructor(
    public snackBar: MatSnackBar,
    private route: ActivatedRoute,
    private router: Router,
    private service: ChurchService,
    public customizer: CustomizerService,
  ) {}

  ngOnInit(): void {}
  submit() {
    try {
      this.loading = true;
      this.service.forgotPassword(this.email.value).subscribe({
        next: (res) => {
          if (!res.status) {
            this.loading = false;
            this.openSnackBar(res.message, 'Try again');
            return;
          }

          this.loading = false;
          this.openSnackBar(res.message, 'ok');
          setTimeout(() => {
            this.router.navigate(['/login']);
          }, 2100);
        },
        error: (err) => {
          this.loading = false;
          this.openSnackBar(err.error.message, 'Try again');
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  getErrorMessage(): string {
    return this.email.hasError('required')
      ? 'You must enter a value'
      : this.email.hasError('email')
      ? 'Not a valid email'
      : '';
  }

  openSnackBar(message: string, action: string) {
    this.snackBar.open(message, action, {
      duration: 2000,
    });
  }
}
