<div fxLayout="row wrap" class="auth-wrapper">
  <div fxFlex="100" fxFlex.gt-md="60%" class="bg-white bg-church-landing-page">
    <div fxFlex="100" class="d-flex align-items-center justify-content-center detail-part">
      <div class="text-center bg-church-landing-page-logo">
        <img src="assets/images/logos/logo.png" />
      </div>
    </div>
  </div>
  <div fxFlex="100" fxFlex.gt-md="40%" class="d-flex align-items-center">
    <div fxLayout="row wrap" class="right-bg-content">
      <div fxFlex="100" fxFlex.sm="70%" fxFlex.gt-md="70%" fxFlex.gt-lg="50%">
        <div class="p-30">
          <h2 class="fw-bold m-b-5 text-primary">Forgot your password?</h2>
          <div class="d-flex align-items-center gap-1">
            <span class="text-muted mat-subheading-2 fw-normal lh-md">
              Please enter the email address associated with your account and We will email you a link to reset your
              password.
            </span>
          </div>
          <form class="m-t-30">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Email Address</mat-label>
              <input matInput placeholder="Enter your email" [formControl]="email" required />
              <mat-error *ngIf="email.invalid">{{ getErrorMessage() }}</mat-error>
            </mat-form-field>
            <button
              mat-flat-button
              color="primary"
              class="w-100 orange-primary"
              [class.spinner]="loading"
              [disabled]="!email.valid || loading"
              (click)="submit()">
              Submit
            </button>
            <a routerLink="/login">
              <button mat-button color="primary" class="w-100 m-t-15 btn-back">Back to Login</button>
            </a>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
