import { Component, OnInit } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, FormGroupDirective, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ProfileService } from 'src/app/services/profile.service';
import { IAPIResponse } from 'src/app/types/interfaces/api.interface';
import { IITCollection } from 'src/app/types/interfaces/it.interface';
import Validation from 'src/app/validators/confirmed.validator';

@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.scss'],
  standalone: false
})
export class UserProfileComponent implements OnInit {
  profileForm: UntypedFormGroup = new UntypedFormGroup({});
  passwordForm: UntypedFormGroup = new UntypedFormGroup({});

  userDataSubmitted = false;
  passwordDataSubmitted = false;

  userId: string = '';
  editFormLoading: boolean = false;
  pwdFormLoading: boolean = false;

  profileFormData!: IITCollection;
  passwordFormData!: IITCollection;

  isLoading: boolean = false;

  constructor(private profileService: ProfileService, private fb: UntypedFormBuilder, public snackBar: MatSnackBar) {
    this.userId = this.profileService.getAdminId() || '';
  }

  ngOnInit(): void {
    this.profileForm = this.fb.group({
      name: [''],
      email: ['', Validators.email],
      mobile: ['', [Validators.pattern('^((\\+91-?)|0)?[0-9]{10}$')]],
    });

    this.passwordForm = this.fb.group(
      {
        newPassword: ['', [Validators.required, Validators.minLength(6)]],
        confirmNewPassword: ['', Validators.required],
      },
      {
        validators: [Validation.match('newPassword', 'confirmNewPassword')],
      },
    );

    this.isLoading = true;
    this.profileService.getUserById(this.userId).subscribe((res) => {
      if (!res.data) {
        this.isLoading = false;
        return;
      }
      this.isLoading = false;

      this.profileForm.patchValue({
        name: res.data.name,
        email: res.data.email,
      });
    });
  }

  get editFormControl(): { [key: string]: AbstractControl } {
    return this.profileForm.controls;
  }
  get passwordFormControl(): { [key: string]: AbstractControl } {
    return this.passwordForm.controls;
  }

  editFormSubmit() {
    try {
      this.userDataSubmitted = true;
      if (this.profileForm.invalid) {
        return;
      }

      this.profileFormData = this.profileForm.value;
      if (this.profileForm.value) {
        this.editFormLoading = true;
      }

      this.profileService.updateUserDetails(this.userId, this.profileFormData).subscribe({
        next: (res) => {
          let response = <IAPIResponse>res;
          if (!response.status) {
            this.editFormLoading = false;
            this.openSnackBar(response.message, 'Try again');
            return;
          }

          this.editFormLoading = false;
          this.profileService.updateAdminName('form submitted');
          this.openSnackBar('Profile Updated', 'Success');
        },
        error: (err) => {
          this.editFormLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (error) {
      console.error(error);
      this.editFormLoading = false;
      this.openSnackBar('Something went wrong', 'Try again');
    }
  }

  passwordFormSubmit(formDirective: FormGroupDirective) {
    try {
      this.passwordDataSubmitted = true;
      if (this.passwordForm.invalid) {
        return;
      }

      this.passwordFormData = this.passwordForm.value;
      if (this.passwordForm.value) {
        this.pwdFormLoading = true;
      }

      this.profileService.updateUserPassword(this.userId, this.passwordFormData).subscribe({
        next: (res) => {
          this.passwordDataSubmitted = false;
          if (!res.status) {
            this.pwdFormLoading = false;
            this.openSnackBar(res.message, 'Try again');
            return;
          }

          this.pwdFormLoading = false;
          this.openSnackBar('Password Updated', 'Success');
          formDirective.resetForm();
        },
        error: (err) => {
          this.pwdFormLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (error) {
      console.error(error);
      this.pwdFormLoading = false;
      this.openSnackBar('Something went wrong', 'Try again');
    }
  }

  openSnackBar(message: string, action: string) {
    this.snackBar.open(message, action, {
      duration: 1000,
    });
  }
}
