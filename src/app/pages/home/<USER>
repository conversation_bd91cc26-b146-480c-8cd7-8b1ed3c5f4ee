import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { SpinnerModule } from 'src/app/components/spinner/spinner.module';
import { MaterialModule } from 'src/app/material/material.module';

import { DialogComponent } from './../../components/church/dialog/dialog.component';
import { ListChurch } from './../../components/church/list/list-church.component';
import { HomeComponent } from './home.component';
import { homeRoutes } from './home.routing';

@NgModule({
  declarations: [HomeComponent, ListChurch, DialogComponent],
  imports: [
    SpinnerModule,
    RouterModule.forChild(homeRoutes),
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    FeatherModule.pick(allIcons),
  ],
})
export class HomeModule {}
