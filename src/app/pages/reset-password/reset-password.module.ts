import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { SpinnerModule } from 'src/app/components/spinner/spinner.module';
import { MaterialModule } from 'src/app/material/material.module';

import { ResetPasswordComponent } from './reset-password.component';
import { resetPasswordRoutes } from './reset-password.routing';

@NgModule({
  declarations: [ResetPasswordComponent],
  imports: [
    SpinnerModule,
    RouterModule.forChild(resetPasswordRoutes),
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    FeatherModule.pick(allIcons),
  ],
})
export class ResetPasswordModule {}
