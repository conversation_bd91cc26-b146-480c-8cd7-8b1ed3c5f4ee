<div *ngIf="isLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>
<div fxLayout="row wrap" class="auth-wrapper">
  <div fxFlex="100" fxFlex.gt-md="60%" class="bg-white bg-church-landing-page">
    <div fxFlex="100" class="d-flex align-items-center justify-content-center detail-part">
      <div class="text-center bg-church-landing-page-logo">
        <img src="assets/images/logos/logo.png" />
      </div>
    </div>
  </div>
  <div fxFlex="100" fxFlex.gt-md="40%" class="d-flex align-items-center">
    <div fxLayout="row wrap" class="right-bg-content">
      <div fxFlex="100" fxFlex.sm="70%" fxFlex.gt-md="70%" fxFlex.gt-lg="50%">
        <!-- <div class="auth-logo-main">
          <img
            src="https://tithing-apps-church-resources-prod.s3.amazonaws.com/629269b6ec901a6259e65460/logo/629269b6ec901a6259e65460.png"
            alt="logo" />
        </div> -->
        <div class="p-30">
          <div *ngIf="isToken; else elseToken">
            <form class="m-t-30" [formGroup]="resetPasswordForm" class="m-t-30" (ngSubmit)="onSubmit()">
              <h2 class="fw-bold m-b-10 text-primary text-center">Reset Password</h2>
              <mat-form-field appearance="outline">
                <mat-label>Password</mat-label>
                <input
                  matInput
                  type="password"
                  formControlName="password"
                  name="password"
                  placeholder="Password"
                  [ngClass]="{ 'is-invalid': submitted && f['password'].errors }"
                  [type]="passwordHide ? 'password' : 'text'" />
                <mat-icon class="text-primary password-icon" matSuffix (click)="passwordHide = !passwordHide">{{
                  passwordHide ? 'visibility' : 'visibility_off'
                }}</mat-icon>
              </mat-form-field>

              <div *ngIf="submitted && f['password'].errors" class="invalid-feedback">
                <div *ngIf="f['password'].errors['required']">Password is required</div>
                <div *ngIf="f['password'].errors['minlength']">Password must be at least 6 characters</div>
              </div>
              <mat-form-field appearance="outline">
                <mat-label>Confirm Password</mat-label>
                <input
                  matInput
                  type="password"
                  formControlName="confirmPassword"
                  name="confirmPassword"
                  placeholder="Confirm Password"
                  [ngClass]="{
                    'is-invalid': submitted && f['confirmPassword'].errors
                  }"
                  [type]="confirmPasswordHide ? 'password' : 'text'" />
                <mat-icon
                  class="text-primary password-icon"
                  matSuffix
                  (click)="confirmPasswordHide = !confirmPasswordHide"
                  >{{ confirmPasswordHide ? 'visibility' : 'visibility_off' }}</mat-icon
                >
              </mat-form-field>
              <div *ngIf="submitted && f['confirmPassword'].errors" class="invalid-feedback">
                <div *ngIf="f['confirmPassword'].errors['required']">Confirm Password is required</div>
                <div *ngIf="f['confirmPassword'].errors['matching']">Password and confirm password not match</div>
              </div>

              <button mat-flat-button color="primary" class="w-100" type="submit">Reset</button>
            </form>
          </div>
          <ng-template #elseToken>
            <div class="m-t-30">
              <div class="error-card text-center">
                <div *ngIf="isSuccess; else elseCondition">
                  <mat-icon class="text-success">done</mat-icon>
                </div>
                <ng-template #elseCondition>
                  <mat-icon color="warn" class="mat-icon notranslate material-icons mat-icon-no-color"
                    >highlight_off</mat-icon
                  >
                </ng-template>
                <h2 class="mat-h2">{{ msg }} !</h2>
              </div>
            </div>
          </ng-template>
          <a routerLink="/">
            <button mat-button color="primary" class="w-100 m-t-15">Back to Login</button>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
