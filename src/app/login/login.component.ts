import { Component, OnInit } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/services/auth.service';
import { CustomizerService } from 'src/app/services/customizer.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  providers: [AuthService],
  standalone: false
})
export class LoginComponent implements OnInit {
  msg = '';
  isLoading: boolean = false;

  constructor(
    private snackBar: MatSnackBar,
    private router: Router,
    public customizer: CustomizerService,
    private service: AuthService,
  ) {}

  ngOnInit(): void {}

  check(username: string, password: string) {
    try {
      this.isLoading = true;
      this.service.signIn({ username, password }).subscribe({
        next: (output) => {
          if (output) {
            this.isLoading = false;
            this.router.navigate(['/home']);
            return;
          }

          this.isLoading = false;
          this.msg = 'Invalid Username or Password';
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }
}
