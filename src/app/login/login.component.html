<div *ngIf="isLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>

<div fxLayout="row wrap" class="auth-wrapper">
  <div fxFlex="100" fxFlex.gt-md="60%" class="bg-white bg-church-landing-page">
    <div fxFlex="100" class="d-flex align-items-center justify-content-center detail-part">
      <div class="text-center bg-church-landing-page-logo">
        <img src="assets/images/logos/logo.png" />
      </div>
    </div>
  </div>

  <div fxFlex="100" fxFlex.gt-md="40%" class="d-flex align-items-center">
    <div fxLayout="row wrap" class="right-bg-content">
      <div fxFlex="100" fxFlex.sm="70%" fxFlex.gt-md="70%" fxFlex.gt-lg="50%">
        <div class="p-30">
          <h2 class="fw-bold m-b-5 text-center text-primary">Sign In</h2>

          <form class="m-t-30">
            <div *ngIf="msg" class="bg-danger p-10 text-white">{{ msg }}</div>
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Username</mat-label>
              <input matInput required="" #u1 />
            </mat-form-field>
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Password</mat-label>
              <input matInput type="password" required="" #p2 />
            </mat-form-field>
            <div class="hstack hstack-md m-t-10 m-b-15">
              <a routerLink="/forgot-password" class="text-accent mat-subheading-2 fw-medium">Forgot Password ?</a>
            </div>
            <button mat-flat-button color="primary" class="w-100 orange-primary" (click)="check(u1.value, p2.value)">Sign In</button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
