export interface IChurchCollection {
  _id: string;
  name: string;
  email: string;
  domain: string;
  content: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  coverImage: string;
  isActivationCompleted: boolean;
  logo: string | ArrayBuffer | null;
  mobile: string;
  password: string;
  login: boolean;
}

export interface IChurchWithAction extends IChurchCollection {
  action: string;
}

export interface IChurchUpload {
  churchData: IChurchCollection;
  uploadImage: File;
}
