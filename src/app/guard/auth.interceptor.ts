import { HttpErrorResponse, HttpEvent, Http<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { AuthService } from '../services/auth.service';
import { AuthUtils } from '../utils/auth.utils';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private authService: AuthService, private router: Router) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    let reqClone = req.clone();
    const authStorage = AuthUtils.getAuthStorage();

    if (authStorage?.token && !AuthUtils.isTokenExpired()) {
      const headers = req.headers.set('Authorization', `Bearer ${authStorage.token}`);
      reqClone = req.clone({ headers });
    }

    return next.handle(reqClone).pipe(
      catchError((error) => {
        if (error instanceof HttpErrorResponse && error.status === 401) {
          this.authService.signOut();
          this.router.navigate(['login']);
        }

        return throwError(() => error);
      }),
    );
  }
}
