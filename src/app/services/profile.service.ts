import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';

import { baseUrl } from '../config/app.config';
import { IAPIResponse } from '../types/interfaces/api.interface';
import { IITCollection } from '../types/interfaces/it.interface';
import { AuthUtils } from '../utils/auth.utils';

@Injectable({
  providedIn: 'root',
})
export class ProfileService {
  public editDataDetails: any = [];
  public subject = new Subject<any>();

  constructor(private httpClient: HttpClient) {}

  getUserById(id: string) {
    return this.httpClient.get<IAPIResponse<IITCollection>>(`${baseUrl}/${id}`);
  }

  updateUserDetails(id: string, editFormData: IITCollection) {
    const updatedUserData = {
      name: editFormData.name,
      email: editFormData.email,
    };

    return this.httpClient.put<IAPIResponse<IITCollection[]>>(`${baseUrl}/${id}`, updatedUserData);
  }

  updateUserPassword(id: string, passwordFormData: IITCollection) {
    const updatedUserPassword = {
      password: passwordFormData.newPassword,
    };

    return this.httpClient.put<IAPIResponse<IITCollection[]>>(`${baseUrl}/${id}`, updatedUserPassword);
  }

  private getProfileMsg = new BehaviorSubject(this.editDataDetails);

  profileTrigger = this.getProfileMsg.asObservable();
  updateAdminName(message: string) {
    this.getProfileMsg.next(message);
  }

  getAdminId() {
    const adminAuthData = AuthUtils.getAuthStorage();
    return adminAuthData?.userId;
  }
}
