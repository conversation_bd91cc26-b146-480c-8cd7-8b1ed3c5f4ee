import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { baseUrl } from '../config/app.config';
import { IAdminCollection } from '../types/interfaces/admin.interface';
import { IAPIResponse, IPaginationResponse } from '../types/interfaces/api.interface';
import { IChurchCollection } from '../types/interfaces/church.interface';
import { IUserCollection } from '../types/interfaces/user.interface';
import { AuthUtils } from '../utils/auth.utils';

@Injectable({
  providedIn: 'root',
})
export class AdminService {
  constructor(private httpClient: HttpClient) {}

  updateChurchAdmin(token: string, formData: IChurchCollection) {
    const churchAdminData = {
      name: formData.name,
      email: formData.email,
      mobile: formData.mobile,
      password: formData.password,
    };

    return this.httpClient.put<IAPIResponse<IUserCollection[]>>(`${baseUrl}/signup/` + token, churchAdminData);
  }

  getAllAdmins(currentPage: number, pageSize: number, search: string) {
    return this.httpClient.get<IAPIResponse<IPaginationResponse<IUserCollection[]>>>(
      `${baseUrl}/list?currentPage=${currentPage}&pageSize=${pageSize}&search=${search}`,
    );
  }

  activateOrDeactivateUser(id: string, isActive: boolean) {
    const isActives = { isActive: isActive };
    return this.httpClient.put<IAPIResponse>(`${baseUrl}/deactivate-admin/${id}`, isActives);
  }

  updateUser(data: IUserCollection | IAdminCollection) {
    const dataPass = {
      name: data.name,
      email: data.email,
      mobile: data.mobile,
    };

    return this.httpClient.put<IAPIResponse>(`${baseUrl}/${data._id}`, dataPass);
  }

  addUser(data: IAdminCollection) {
    const dataPass = {
      name: data.name,
      email: data.email,
      mobile: data.mobile,
      password: '',
    };
    return this.httpClient.post<IAPIResponse>(`${baseUrl}/register`, dataPass);
  }

  deleteAdmin(_id: string) {
    return this.httpClient.delete<IAPIResponse>(`${baseUrl}/admin/${_id}`);
  }

  forgotPassword(email: string) {
    return this.httpClient.put<IAPIResponse>(`${baseUrl}/forgot-password/${email}`, null);
  }

  getAdminId() {
    const adminAuthData = AuthUtils.getAuthStorage();
    return adminAuthData?.userId;
  }

  resendEmail(_id: string) {
    return this.httpClient.post<IAPIResponse>(`${baseUrl}/resend-email/${_id}`, null);
  }
}
