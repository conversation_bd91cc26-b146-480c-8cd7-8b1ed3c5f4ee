import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { baseUrl } from '../config/app.config';
import { IResetPasswordCollection } from '../types/interfaces/resetpassword.interface';
import { IAPIResponse } from './../types/interfaces/api.interface';
import { IChurchCollection } from './../types/interfaces/church.interface';
import { IVersionCollection, IVersionGetCollection } from './../types/interfaces/version.interface';

@Injectable({
  providedIn: 'root',
})
export class ChurchService {
  constructor(private httpClient: HttpClient) {}

  getChurches() {
    return this.httpClient.get<IAPIResponse<IChurchCollection[]>>(`${baseUrl}/churches`);
  }

  createChurch(data: IChurchCollection, selectedIMage: File) {
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('email', data.email);
    formData.append('mobile', data.mobile);
    formData.append('login', JSON.stringify(data.login));

    if (data.content) {
      formData.append('content', data.content);
    }

    if (selectedIMage) {
      formData.append('logo', selectedIMage);
    }

    return this.httpClient.post(`${baseUrl}/create-church`, formData);
  }

  updateChurch(data: IChurchCollection, selectedIMage: File) {
    let id = data._id;
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('email', data.email);
    formData.append('mobile', data.mobile);
    formData.append('login', JSON.stringify(data.login));

    if (data.content) {
      formData.append('content', data.content);
    }

    if (selectedIMage) {
      formData.append('logo', selectedIMage);
    }

    return this.httpClient.put(`${baseUrl}/update-church/${id}`, formData);
  }

  disableChurch(id: string, isActive: boolean) {
    const isActives = { isActive: isActive };
    return this.httpClient.put(`${baseUrl}/disable-church/${id}`, isActives);
  }

  forgotPassword(email: string) {
    return this.httpClient.put<IAPIResponse>(`${baseUrl}/forgot-password/${email}`, null);
  }

  resetPassword(data: IResetPasswordCollection) {
    return this.httpClient.put<IAPIResponse>(`${baseUrl}/reset-password/verify`, data);
  }

  tokenVerification(token: string) {
    return this.httpClient.post<IAPIResponse>(`${baseUrl}/token-verification`, { token: token });
  }

  getVersion(id: string) {
    return this.httpClient.get<IAPIResponse<IVersionGetCollection[]>>(`${baseUrl}/app-versions/${id}`);
  }

  manageVersion(data: IVersionCollection) {
    return this.httpClient.post<IAPIResponse>(`${baseUrl}/add-app-version`, data);
  }
}
