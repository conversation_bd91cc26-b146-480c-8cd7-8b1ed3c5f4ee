import { JwtHelperService } from '@auth0/angular-jwt';
import { AES, enc } from 'crypto-js';

import { IAuthToken } from '../types/interfaces/auth.interface';

const KEY = 'session';
const KEY_STRING = 'A3huJaSqoTF925nqMAjiva';

export class AuthUtils {
  constructor() {}

  static setAuthStorage(auth: IAuthToken): boolean {
    try {
      const str = AES.encrypt(JSON.stringify(auth), KEY_STRING).toString();
      localStorage.setItem(KEY, str);
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  static getAuthStorage(): IAuthToken | null {
    try {
      const auth = localStorage.getItem(KEY);
      if (!auth) {
        return null;
      }

      const str = AES.decrypt(auth, KEY_STRING).toString(enc.Utf8);
      const result = JSON.parse(str);
      return result;
    } catch (err) {
      console.log(err);
      return null;
    }
  }

  static clearStorage(key = KEY) {
    localStorage.removeItem(key);
  }

  static isTokenExpired() {
    const t = this.getAuthStorage();
    if (!t) {
      return true;
    }

    const helper = new JwtHelperService();
    return helper.isTokenExpired(t.token);
  }
}
