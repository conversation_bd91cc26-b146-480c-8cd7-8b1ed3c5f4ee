import { Route } from '@angular/router';

import { AuthGuard } from './guard/auth.guard';
import { NoAuthGuard } from './guard/noAuth.guard';
import { FullComponent } from './layouts/full/full.component';
import { LoginComponent } from './login/login.component';

export const appRoutes: Route[] = [
  {
    path: '',
    component: FullComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'login',
        redirectTo: '/login',
        pathMatch: 'full',
      },
      {
        path: '',
        loadChildren: () => import('./pages/pages.module').then((m) => m.PagesModule),
      },
    ],
  },
  {
    path: 'login',
    canActivate: [NoAuthGuard],
    component: LoginComponent,
  },

  {
    path: 'forgot-password',
    loadChildren: () => import('./pages/forgot-password/forgot-password.module').then((m) => m.ForgotModule),
  },
  {
    path: 'reset-password/:token',
    loadChildren: () => import('./pages/reset-password/reset-password.module').then((m) => m.ResetPasswordModule),
  },
  {
    path: '**',
    redirectTo: '',
  },
];
