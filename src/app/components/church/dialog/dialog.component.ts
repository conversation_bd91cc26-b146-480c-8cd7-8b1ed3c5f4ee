import { DatePipe } from '@angular/common';
import { Component, Inject, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ChurchService } from 'src/app/services/church.service';
import { IAPIResponse } from 'src/app/types/interfaces/api.interface';
import { generateCaptcha } from 'src/app/utils/generate-captch';

import { IChurchCollection, IChurchUpload, IChurchWithAction } from './../../../types/interfaces/church.interface';
import { IVersionCollection, IVersionGetCollection } from './../../../types/interfaces/version.interface';

enum Actions {
  ADD_CHURCH = 'Add Church',
  UPDATE_CHURCH = 'Update Church',
  DISABLE = 'Disable',
  ENABLE = 'Enable',
  MOBILE_VERSION = 'Manage Mobile Version',
}

@Component({
  selector: 'app-dialog',
  templateUrl: './dialog.component.html',
  styleUrls: ['./dialog.component.scss'],
  standalone: false
})
export class DialogComponent {
  action: string;
  localData: IChurchWithAction;
  getVersion: IVersionGetCollection[] = [];
  versionData: IVersionCollection = {
    ios: '',
    android: '',
    captcha: '',
    churchId: '',
  };
  imageUpload!: File;
  buttonLoading: boolean = false;
  IsMobileVersion: boolean = false;
  IsCaptcha: boolean = false;
  captchaText: string = '';

  constructor(
    private snackBar: MatSnackBar,
    public datePipe: DatePipe,
    public dialogRef: MatDialogRef<DialogComponent>,
    private churchService: ChurchService,
    private _snackBar: MatSnackBar,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: IChurchWithAction,
  ) {
    this.localData = data;
    this.action = this.localData.action;
    dialogRef.disableClose = true;

    if (this.action === 'Manage Mobile Version') {
      this.IsMobileVersion = true;
      this.versionData.churchId = this.localData._id;
      this.getIOSVersion(this.versionData.churchId);

      this.captchaText = generateCaptcha();
    }
    if (!this.localData.logo) {
      this.localData.logo = 'assets/images/logos/logo.png';
    }
  }

  doAction(): void {
    if (this.localData.action === 'Add Church') {
      if (this.imageUpload === undefined) {
        this._snackBar.open('Please, upload logo', 'ok', {
          duration: 5000,
        });
        return;
      }
    }

    if (this.localData.login === undefined) {
      this.localData.login = false;
    }

    const dataPass = {
      churchData: this.localData,
      uploadImage: this.imageUpload,
    };
    const expression = this.localData.action;
    switch (expression) {
      case Actions.ADD_CHURCH:
        this.addChurch(dataPass);
        break;
      case Actions.UPDATE_CHURCH:
        this.updateChurch(dataPass);
        break;
      case Actions.DISABLE:
      case Actions.ENABLE:
        this.disableChurch(dataPass.churchData);
        break;
      case Actions.MOBILE_VERSION:
        this.manageVersion(this.versionData);
        break;
    }
  }

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' });
  }

  selectImage(event: Event): void {
    const element = event.currentTarget as HTMLInputElement;
    let fileList: FileList | null = element.files;

    if (!fileList || fileList.length === 0) {
      return;
    }

    const mimeType = fileList[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(fileList[0]);
    reader.onload = (_event) => {
      this.localData.logo = reader.result;
    };
    this.imageUpload = <File>fileList[0];
  }

  addChurch(rowObj: IChurchUpload): void {
    try {
      this.buttonLoading = true;
      this.churchService.createChurch(rowObj.churchData, rowObj.uploadImage).subscribe({
        next: (res) => {
          let response = <IAPIResponse>res;
          this.buttonLoading = false;
          if (response.status === false) {
            this.buttonLoading = false;
            this._snackBar.open(response.message, 'ok', {
              duration: 6000,
            });
            return 'error';
          }

          this._snackBar.open(response.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();

          return 'success';
        },
        error: (err) => {
          this.buttonLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.buttonLoading = false;
      console.error(err);
    }
  }

  updateChurch(rowObj: IChurchUpload): void {
    try {
      this.buttonLoading = true;
      this.churchService.updateChurch(rowObj.churchData, rowObj.uploadImage).subscribe({
        next: (res) => {
          let response = <IAPIResponse>res;
          if (response.status === false) {
            this.buttonLoading = false;
            this._snackBar.open(response.message, 'ok', {
              duration: 6000,
            });
            return 'error';
          }
          this.buttonLoading = false;
          this._snackBar.open(response.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();

          return 'success';
        },
        error: (err) => {
          this.buttonLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.buttonLoading = false;
      console.error(err);
    }
  }

  disableChurch(data: IChurchCollection) {
    try {
      data.isActive = !data.isActive;
      this.buttonLoading = true;
      this.churchService.disableChurch(data._id, data.isActive).subscribe({
        next: (res) => {
          let response = <IAPIResponse>res;
          if (response.status === false) {
            this.buttonLoading = false;
            this._snackBar.open(response.message, 'ok', {
              duration: 6000,
            });
            return 'error';
          }
          this.buttonLoading = false;
          this._snackBar.open(response.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();

          return 'success';
        },
        error: (err) => {
          this.buttonLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.buttonLoading = false;
      console.error(err);
    }
  }

  checkValue(e: any) {
    if (e.checked === true) {
      // this.isChecked = true;
      this.localData.login = true;
    } else {
      // this.isChecked = false;
      this.localData.login = false;
    }
  }

  getIOSVersion(id: string) {
    try {
      this.churchService.getVersion(id).subscribe((res) => {
        if (!res.status || !res.data) {
          return;
        }
        this.getVersion = res.data;
        //        this.versionData = this.getVersion.
        this.getVersion.forEach((value) => {
          if (value.type === 'ios') {
            this.versionData.ios = value.version;
            return;
          }
          this.versionData.android = value.version;
        });
        // this.versionData. = res.data;
      });
    } catch (err) {
      console.error(err);
    }
  }

  onCaptchaChange(captcha: Event) {
    if (this.versionData.captcha === this.captchaText) {
      this.IsCaptcha = true;
    }
  }

  manageVersion(data: IVersionCollection): void {
    try {
      this.buttonLoading = true;
      this.churchService.manageVersion(data).subscribe({
        next: (res) => {
          let response = <IAPIResponse>res;
          this.buttonLoading = false;
          if (response.status === false) {
            this.buttonLoading = false;
            this._snackBar.open(response.message, 'ok', {
              duration: 6000,
            });
            return 'error';
          }

          this._snackBar.open(response.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();

          return 'success';
        },
        error: (err) => {
          this.buttonLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.buttonLoading = false;
      console.error(err);
    }
  }
}
