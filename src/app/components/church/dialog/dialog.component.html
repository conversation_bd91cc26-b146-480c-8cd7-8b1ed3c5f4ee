<h2 class="font-medium" mat-dialog-title>
  <strong>{{ action }}</strong>
</h2>
<div
  role="alert"
  class="alert alert-danger alert-text-danger align-items-center d-flex fs-14 mb-16 rounded ng-star-inserted p-5">
  <!-- <i-feather class="feather-base me-12 text-danger">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="feather feather-alert-circle">
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="12" y1="8" x2="12" y2="12"></line>
      <line x1="12" y1="16" x2="12.01" y2="16"></line></svg
  ></i-feather> -->
  <i-feather name="alert-circle" class="text-danger feather-18 m-r-5"></i-feather>

  Please don't change this value without contacting the development team. This value should be the same as the mobile
  app versions in the AppStore and PlayStore.
</div>

<div *ngIf="!IsMobileVersion; else mobileVersionTemplate">
  <div class="pb-3" *ngIf="action === 'Add Church' || action === 'Update Church'; else elseTemplate">
    <form #churchForm="ngForm" novalidate>
      <mat-dialog-content>
        <div class="d-flex align-items-center m-b-15">
          <img class="mat-card-avatar" [src]="localData.logo" />
          <button mat-raised-button color="primary" class="m-l-15 input-file-button">
            <input
              type="file"
              accept="image/png, image/jpeg, image/jpg"
              required
              (change)="selectImage($event)"
              #fileInput />
          </button>
        </div>
        <div fxLayout="row wrap" class="align-items-center">
          <div fxFlex="100" fxFlex.gt-md="100">
            <div class="m-r-15 m-l-15">
              <mat-form-field>
                <input
                  type="text"
                  matInput
                  required
                  id="name"
                  name="name"
                  [(ngModel)]="localData.name"
                  placeholder="Name" />
              </mat-form-field>
            </div>
          </div>
          <div fxFlex="100" fxFlex.gt-md="100">
            <div class="m-r-15 m-l-15">
              <mat-form-field>
                <input
                  type="email"
                  matInput
                  required
                  id="email"
                  name="email"
                  placeholder="Email"
                  required
                  email
                  #userEmail="ngModel"
                  [(ngModel)]="localData.email" />
              </mat-form-field>
              <span *ngIf="!userEmail.valid && userEmail.touched" class="help-block text-danger"
                >Enter a valid Email</span
              >
            </div>
          </div>

          <div fxFlex="100" fxFlex.gt-md="100">
            <div class="m-r-15 m-l-15">
              <mat-form-field>
                <input
                  matInput
                  oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');"
                  type="text"
                  maxlength="10"
                  minlength="10"
                  required
                  id="mobile"
                  name="mobile"
                  #userMobile="ngModel"
                  [(ngModel)]="localData.mobile"
                  placeholder="mobile" />
              </mat-form-field>
              <div *ngIf="userMobile.touched && userMobile.invalid">
                <span class="help-block text-danger" ng-show="churchForm.mobile.$error.minlength"
                  >Please enter 10 digit mobile number.</span
                >
              </div>
            </div>
          </div>

          <div fxFlex="100" fxFlex.gt-md="100">
            <div class="m-r-15 m-l-15">
              <mat-form-field>
                <textarea
                  matInput
                  type="text"
                  matInput
                  id="content"
                  name="content"
                  [(ngModel)]="localData.content"
                  placeholder="content"></textarea>
              </mat-form-field>
            </div>
          </div>
          <section class="example-section">
            <mat-checkbox
              class="example-margin"
              [(ngModel)]="localData.login"
              (change)="checkValue($event)"
              [ngModelOptions]="{ standalone: true }">
              Login Required</mat-checkbox
            >
          </section>
        </div>
      </mat-dialog-content>
      <mat-dialog-actions>
        <button
          mat-button
          (click)="doAction()"
          mat-flat-button
          color="primary"
          [class.spinner]="buttonLoading"
          [disabled]="!churchForm.valid">
          {{ action }}
        </button>
        <button mat-button (click)="closeDialog()">Cancel</button>
      </mat-dialog-actions>
    </form>
  </div>
  <ng-template #elseTemplate>
    <p>
      Sure to {{ action }} <b>{{ localData.name }}</b> ?
    </p>
    <div mat-dialog-actions align="center" class="pt-3">
      <button mat-button (click)="doAction()" [class.spinner]="buttonLoading" mat-flat-button color="primary">
        {{ action }}
      </button>
      <button mat-button (click)="closeDialog()">Cancel</button>
    </div>
  </ng-template>
</div>

<ng-template #mobileVersionTemplate>
  <form #churchVersionForm="ngForm" novalidate>
    <mat-dialog-content>
      <div fxLayout="row wrap" class="align-items-center">
        <div fxFlex="100" fxFlex.gt-md="100">
          <div class="m-r-15 m-l-15 m-t-5">
            <mat-form-field>
              <input
                type="text"
                matInput
                id="android"
                name="android"
                [(ngModel)]="versionData.android"
                placeholder="Android Version" />
            </mat-form-field>
          </div>
        </div>

        <div fxFlex="100" fxFlex.gt-md="100">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input type="text" matInput id="ios" name="ios" [(ngModel)]="versionData.ios" placeholder="IOS Version" />
            </mat-form-field>
          </div>
        </div>

        <div fxFlex="100" fxFlex.gt-md="100">
          <div class="m-r-15 m-l-15">
            <mat-label
              >Please enter
              <b
                ><i>{{ captchaText }}</i></b
              >
              this text in the box below
            </mat-label>
          </div>
        </div>

        <div fxFlex="100" fxFlex.gt-md="100" class="m-t-5">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                type="text"
                matInput
                required
                id="captcha"
                name="captcha"
                [(ngModel)]="versionData.captcha"
                placeholder="Enter {{ captchaText }}"
                (ngModelChange)="onCaptchaChange($event)" />
            </mat-form-field>
          </div>
        </div>
      </div>
    </mat-dialog-content>
  </form>
  <div mat-dialog-actions align="center" class="pt-3">
    <button mat-button class="primary" [class.spinner]="buttonLoading" [disabled]="!IsCaptcha" (click)="doAction()">
      Save
    </button>
    <button mat-button (click)="closeDialog()">Cancel</button>
  </div>
</ng-template>
