import { DatePipe } from '@angular/common';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { defaultDomain, urlPrefix } from 'src/app/config/app.config';

import { ChurchService } from './../../../services/church.service';
import { IChurchCollection } from './../../../types/interfaces/church.interface';
import { DialogComponent } from './../dialog/dialog.component';

@Component({
  templateUrl: './list-church.component.html',
  styleUrls: ['./list-church.component.scss'],
  selector: 'app-church-list',
  standalone: false
})
export class ListChurch implements OnInit, AfterViewInit {
  churches: IChurchCollection[] = [];
  defaultDomain = '';
  prefix = '';

  @ViewChild(MatTable, { static: true }) table: MatTable<any> = Object.create(null);
  displayedColumns: string[] = [
    '#',
    'name',
    'email',
    'mobile',
    'isActivationCompleted',
    'createdAt',
    'isActive',
    'action',
  ];
  isLoading: boolean = false;
  dataSource = new MatTableDataSource(this.churches);
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator = Object.create(null);

  constructor(public dialog: MatDialog, public datePipe: DatePipe, private churchService: ChurchService) {}

  ngOnInit(): void {
    this.prefix = urlPrefix;
    this.defaultDomain = defaultDomain;
    this.getChurches();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
  }

  applyFilter(filterValue: string): void {
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  openDialog(action: string, obj: Object): void {
    let data = { ...obj, action };
    const dialogRef = this.dialog.open(DialogComponent, { data: data });
    dialogRef.afterClosed().subscribe((result) => {
      this.getChurches();
    });
  }

  getChurches() {
    try {
      this.isLoading = true;
      this.churchService.getChurches().subscribe((res) => {
        if (!res.status || !res.data) {
          this.isLoading = false;
          return;
        }

        this.isLoading = false;
        this.dataSource.data = res.data;
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }
}
