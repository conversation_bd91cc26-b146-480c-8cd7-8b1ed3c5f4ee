<div *ngIf="isLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>

<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card>
      <mat-card-content>
        <div fxLayout="row wrap" class="align-items-center">
          <div fxFlex.gt-md="25" fxFlex.gt-lg="25" fxFlex="100">
            <mat-form-field>
              <input matInput placeholder="Search Church" (keyup)="applyFilter($any($event.target).value)" />
            </mat-form-field>
          </div>
          <div fxFlex.gt-md="75" class="text-right">
            <button mat-flat-button (click)="openDialog('Add Church', {})" color="primary">Add Church</button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card>
      <mat-card-content>
        <div class="table-responsive">
          <table mat-table [dataSource]="dataSource" class="w-100">
            <ng-container matColumnDef="#">
              <th mat-header-cell *matHeaderCellDef>#</th>
              <td mat-cell *matCellDef="let element; let i = index">
                {{
                  dataSource.paginator?.pageIndex === 0
                    ? i + 1
                    : 1 + i + (dataSource.paginator?.pageIndex || 0) * (dataSource.paginator?.pageSize || 0)
                }}
              </td>
            </ng-container>
            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef>Name</th>
              <td mat-cell *matCellDef="let element">
                <div class="d-flex align-items-center">
                  <img
                    class="mat-card-avatar"
                    alt="logo"
                    [src]="element.logo"
                    onerror="src='assets/images/logos/logo.png'" />
                  <a
                    class="m-l-15 text-accent"
                    href="{{ prefix }}{{ element.domain }}.{{ defaultDomain }}"
                    target="_blank">
                    <p class="fw-medium mat-subheading-1 m-b-0 m-t-0 text-capitalize">{{ element.name }}</p>
                    <small class="text-muted mat-body-1">{{ element.domain }}</small>
                  </a>
                </div>
              </td>
            </ng-container>

            <ng-container matColumnDef="email">
              <th mat-header-cell *matHeaderCellDef>Email</th>
              <td mat-cell *matCellDef="let element">{{ element.email }}</td>
            </ng-container>

            <ng-container matColumnDef="mobile">
              <th mat-header-cell *matHeaderCellDef>Mobile</th>
              <td mat-cell *matCellDef="let element">{{ element.mobile }}</td>
            </ng-container>

            <ng-container matColumnDef="isActivationCompleted">
              <th mat-header-cell *matHeaderCellDef>Activation</th>
              <td mat-cell *matCellDef="let element">
                <mat-chip
                  class="bg-success text-white"
                  selected="true"
                  *ngIf="element.isActivationCompleted; else isActivationCompleted">
                  Activated
                </mat-chip>
                <ng-template #isActivationCompleted>
                  <mat-chip color="primary" selected="true">Pending</mat-chip>
                </ng-template>
              </td>
            </ng-container>

            <ng-container matColumnDef="createdAt">
              <th mat-header-cell *matHeaderCellDef>Created On</th>
              <td mat-cell *matCellDef="let element">
                {{ element.createdAt | date: 'fullDate' }}
              </td>
            </ng-container>

            <ng-container matColumnDef="isActive">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let element">
                <i-feather
                  *ngIf="element.isActive; else isActive"
                  name="check"
                  class="text-success feather-18"></i-feather>
                <ng-template #isActive>
                  <i-feather name="x" class="text-danger feather-18"></i-feather>
                </ng-template>
              </td>
            </ng-container>

            <ng-container matColumnDef="action">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let element" class="action-link">
                <a (click)="openDialog('Update Church', element)" class="m-r-10 cursor-pointer" title="Edit">
                  <i-feather name="edit" class="text-primary feather-18"></i-feather>
                </a>

                <a
                  *ngIf="element.isActive; else isActive"
                  (click)="openDialog('Disable', element)"
                  class="m-r-10 cursor-pointer"
                  title="Disable">
                  <i-feather name="eye-off" class="text-danger feather-18"></i-feather>
                </a>
                <ng-template #isActive>
                  <a (click)="openDialog('Enable', element)" class="m-r-10 cursor-pointer" title="Enable">
                    <i-feather name="eye" class="text-success feather-18"></i-feather> </a
                ></ng-template>
                <a
                  (click)="openDialog('Manage Mobile Version', element)"
                  class="m-r-10 cursor-pointer"
                  title="Add Mobile Version">
                  <i-feather name="smartphone" class="text-primary feather-18"></i-feather>
                </a>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
          <mat-paginator [pageSizeOptions]="[12, 24, 36, 48, 60]" showFirstLastButtons></mat-paginator>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
