::ng-deep{
    .mat-card-avatar{
        height: 40px;
        width: 40px;
        border-radius: 50%;
        flex-shrink: 0;
        object-fit: cover;
    }

    // Custom search field styling to match first image
    .mat-mdc-form-field {
        width: 300px;

        .mat-mdc-text-field-wrapper {
            background-color: #f0f0f5 !important;
            border-radius: 20px !important;
            padding: 0 16px !important;

            .mdc-notched-outline {
                display: none !important; // Hide the outline border
            }

            .mat-mdc-form-field-input-control {
                .mat-mdc-input-element {
                    padding: 12px 0 !important;
                    font-size: 14px !important;
                    color: #666 !important;

                    &::placeholder {
                        color: #999 !important;
                        opacity: 1 !important;
                    }
                }
            }
        }

        .mat-mdc-floating-label {
            display: none !important; // Hide the floating label
        }

        // Remove focus outline
        &.mat-focused .mat-mdc-text-field-wrapper {
            background-color: #e8e8f0 !important;
        }

        // Remove hover effects
        &:hover .mat-mdc-text-field-wrapper {
            background-color: #e8e8f0 !important;
        }
    }
}