import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { AdminService } from 'src/app/services/admin.service';
import { IAdminCollection } from 'src/app/types/interfaces/admin.interface';

import { DialogAdminComponent } from './dialog-admin/dialog-admin.component';

@Component({
  selector: 'app-admin',
  templateUrl: './admin.component.html',
  styleUrls: ['./admin.component.scss'],
  standalone: false
})
export class AdminComponent implements OnInit, AfterViewInit {
  totalRows = 0;
  currentPage = 0;
  searchText = '';
  isLoading: boolean = false;
  noData: boolean = false;
  pageSizeOptions = [25, 50, 75, 100];
  pageSize = this.pageSizeOptions[0];

  @ViewChild(MatTable, { static: true }) table: MatTable<IAdminCollection> = Object.create(null);
  displayedColumns: string[] = ['#', 'name', 'email', 'emailVerificationStatus', 'status', 'action'];
  dataSource = new MatTableDataSource();
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator = Object.create(null);

  constructor(public dialog: MatDialog, private service: AdminService) {}

  ngOnInit(): void {
    this.getUsers(this.currentPage, this.pageSize, this.searchText);
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
  }

  applyFilter(filterValue: string): void {
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  openDialog(action: string, data: Object): void {
    let obj = { ...data, action };
    obj.action = action;
    const dialogRef = this.dialog.open(DialogAdminComponent, { data: obj });
    dialogRef.afterClosed().subscribe((result) => {
      this.getUsers(this.currentPage, this.pageSize, this.searchText);
    });
  }

  getUsers(page: number, pageSize: number, search: string) {
    try {
      this.isLoading = true;
      this.service.getAllAdmins(page, pageSize, search).subscribe({
        next: (res) => {
          if (!res.status || !res.data) {
            this.isLoading = false;
            this.noData = true;
            return;
          }

          this.noData = false;
          this.isLoading = false;
          this.dataSource.data = res.data?.result || [];
          setTimeout(() => {
            this.paginator.pageIndex = this.currentPage;
            this.paginator.length = res.data?.total || 0;
            const total = res.data?.total ? res.data?.total / this.pageSize : 0;
            this.totalRows = Math.ceil(total);
          });
        },
        error: (err) => {
          this.isLoading = false;
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  pageChanged(event: PageEvent) {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.getUsers(this.currentPage, this.pageSize, this.searchText);
  }

  searchUser() {
    this.getUsers(this.currentPage, this.pageSize, this.searchText);
    this.paginator.firstPage();
  }

  clearSearch() {
    this.searchText = '';
    this.getUsers(this.currentPage, this.pageSize, this.searchText);
  }
}
