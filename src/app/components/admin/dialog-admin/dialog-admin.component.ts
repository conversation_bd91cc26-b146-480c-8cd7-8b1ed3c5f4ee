import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AdminService } from 'src/app/services/admin.service';
import { IAdminCollection, IAdminWithAction } from 'src/app/types/interfaces/admin.interface';

enum Actions {
  ACTIVATE = 'Activate Admin',
  ADD = 'Add Admin',
  DEACTIVATE = 'Deactivate Admin',
  UPDATE = 'Update Admin',
  DELETE = 'Delete Admin',
  RESEND = 'Resend Admin',
}

@Component({
  selector: 'app-dialog-admin',
  templateUrl: './dialog-admin.component.html',
  styleUrls: ['./dialog-admin.component.scss'],
  standalone: false
})
export class DialogAdminComponent implements OnInit {
  adminData: IAdminWithAction;
  isLoading: boolean = false;
  constructor(
    public dialogRef: MatDialogRef<DialogAdminComponent>,
    private adminservice: AdminService,
    private snackBar: MatSnackBar,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: IAdminWithAction,
  ) {
    this.adminData = data;
    dialogRef.disableClose = true;
  }

  ngOnInit(): void {}

  doAction(): void {
    const expression = this.adminData.action;
    switch (expression) {
      case Actions.ACTIVATE:
      case Actions.DEACTIVATE:
        this.activateOrDeactivateUser(this.adminData);
        break;
      case Actions.ADD:
        this.addUser(this.adminData);
        break;
      case Actions.UPDATE:
        this.updateUser(this.adminData);
        break;
      case Actions.RESEND:
        this.resendEmail(this.adminData);
        break;
      case Actions.DELETE:
        this.deleteItAdmin(this.adminData);
        break;
    }
  }

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' });
  }

  activateOrDeactivateUser(data: IAdminWithAction) {
    try {
      data.isActive = !data.isActive;
      this.isLoading = true;
      this.adminservice.activateOrDeactivateUser(data._id, data.isActive).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  addUser(data: IAdminCollection) {
    try {
      this.isLoading = true;
      this.adminservice.addUser(data).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  updateUser(data: IAdminCollection) {
    try {
      this.isLoading = true;
      this.adminservice.updateUser(data).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  deleteItAdmin(data: IAdminCollection) {
    try {
      this.isLoading = true;
      this.adminservice.deleteAdmin(data._id).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  resendEmail(data: IAdminCollection) {
    try {
      this.isLoading = true;
      this.adminservice.resendEmail(data._id).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }
}
