<h2 class="font-medium" mat-dialog-title>
  <strong>{{ adminData.action | titlecase }}</strong>
</h2>
<div class="pb-3" *ngIf="adminData.action === 'Add Admin' || adminData.action === 'Update Admin'; else elseTemplate">
  <form #userForm="ngForm">
    <mat-dialog-content>
      <div fxLayout="row wrap" class="align-items-center">
        <div fxFlex="100" fxFlex.gt-md="100">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                type="text"
                matInput
                required
                id="name"
                name="name"
                [(ngModel)]="adminData.name"
                placeholder="Name"
                #title="ngModel" />
            </mat-form-field>
          </div>
        </div>
        <div fxFlex="100" fxFlex.gt-md="100">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                type="email"
                matInput
                required
                id="email"
                name="email"
                placeholder="Email"
                required
                email
                #userEmail="ngModel"
                [(ngModel)]="adminData.email" />
            </mat-form-field>
            <span *ngIf="!userEmail.valid && userEmail.touched" class="help-block text-danger"
              >Enter a valid Email</span
            >
          </div>
        </div>

        <div fxFlex="100" fxFlex.gt-md="100">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                matInput
                type="string"
                maxlength="10"
                minlength="10"
                id="mobile"
                name="mobile"
                [(ngModel)]="adminData.mobile"
                placeholder="Mobile"
                #userMobile="ngModel"
                oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');" />
            </mat-form-field>
            <div *ngIf="userMobile.touched && userMobile.invalid">
              <span class="help-block text-danger" ng-show="userForm.mobile.$error.minlength"
                >Please enter 10 digit mobile number.</span
              >
            </div>
          </div>
        </div>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions>
      <button mat-button (click)="doAction()" mat-flat-button color="primary" [class.spinner]="isLoading">
        {{ adminData.action | titlecase }}
      </button>
      <button mat-button (click)="closeDialog()">Cancel</button>
    </mat-dialog-actions>
  </form>
</div>
<ng-template #elseTemplate>
  <mat-dialog-content>
    <p>
      Sure to {{ adminData.action }} <b>{{ adminData.name }}</b> ?
    </p>
  </mat-dialog-content>
  <div mat-dialog-actions align="center" class="pt-3">
    <button mat-button [class.spinner]="isLoading" (click)="doAction()" mat-flat-button color="primary">
      {{ adminData.action }}
    </button>
    <button mat-button (click)="closeDialog()">Cancel</button>
  </div>
</ng-template>
