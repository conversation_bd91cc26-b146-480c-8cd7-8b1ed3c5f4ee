/* You can add global styles to this file, and also import other style files */

html,
body {
  height: 100%;
}

body {
  margin: 0;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.spinner:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  border-top-color: #fb9778;
  animation: spinner 0.8s linear infinite;
}

.invisible {
  opacity: 0 !important;
}

.auth-logo-main {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  img {
    width: auto;
    height: 96px;
  }
}

.bg-church-landing-page {
  background-color: #2f2e29 !important;
  background-size: cover;
}

.bg-church-landing-page-logo {
  img {
    width: 250px;
    text-align: center;
  }
}
.orange-primary{
    background-color: #fb9778 !important;
    box-shadow: 0 0 #0003, 0 0 #00000024, 0 0 #0000001f;
    color: #fff !important;
    border-radius: 4px !important;
}
.btn-back{
  color: #fb9778 !important;
}
.w-100{
  width: 100%;
}
.mat-drawer-container{
  background-color: #fafafa !important
}
.mat-mdc-menu-panel{
  background-color: #fff !important;
}