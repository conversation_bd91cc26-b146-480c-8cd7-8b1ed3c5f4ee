/* You can add global styles to this file, and also import other style files */

html,
body {
  height: 100%;
}

body {
  margin: 0;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.spinner:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  border-top-color: #fb9778;
  animation: spinner 0.8s linear infinite;
}

.invisible {
  opacity: 0 !important;
}

.auth-logo-main {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  img {
    width: auto;
    height: 96px;
  }
}

.bg-church-landing-page {
  background-color: #2f2e29 !important;
  background-size: cover;
}

.bg-church-landing-page-logo {
  img {
    width: 250px;
    text-align: center;
  }
}
.orange-primary{
    background-color: #fb9778 !important;
    box-shadow: 0 0 #0003, 0 0 #00000024, 0 0 #0000001f;
    color: #fff !important;
    border-radius: 4px !important;
}
.btn-back{
  color: #fb9778 !important;
}
.w-100{
  width: 100%;
}
.ddtitle{
  font-weight: 500 !important;
  font-size: 16px !important;
}
.text-primary{
  font-size: 14px !important;
}
.blue-primary{
  background-color: #03c9d7 !important;
  border-radius: 4px !important;
}

/* Form Field Styling */
.mat-mdc-form-field {
  /* Default border color (unfocused state) */
  .mat-mdc-text-field-wrapper .mdc-notched-outline .mdc-notched-outline__leading,
  .mat-mdc-text-field-wrapper .mdc-notched-outline .mdc-notched-outline__notch,
  .mat-mdc-text-field-wrapper .mdc-notched-outline .mdc-notched-outline__trailing {
    border-color: #fb9778 !important;
  }

  /* Border color when focused */
  &.mat-focused .mat-mdc-text-field-wrapper .mdc-notched-outline .mdc-notched-outline__leading,
  &.mat-focused .mat-mdc-text-field-wrapper .mdc-notched-outline .mdc-notched-outline__notch,
  &.mat-focused .mat-mdc-text-field-wrapper .mdc-notched-outline .mdc-notched-outline__trailing {
    border-color: #fb9778 !important;
  }

  /* Border color on hover */
  &:hover .mat-mdc-text-field-wrapper .mdc-notched-outline .mdc-notched-outline__leading,
  &:hover .mat-mdc-text-field-wrapper .mdc-notched-outline .mdc-notched-outline__notch,
  &:hover .mat-mdc-text-field-wrapper .mdc-notched-outline .mdc-notched-outline__trailing {
    border-color: #fb9778 !important;
  }

  /* Label color when focused */
  &.mat-focused .mat-mdc-floating-label {
    color: #fb9778 !important;
  }
}

/* Input placeholder color */
.mat-mdc-input-element::placeholder {
  color: #fb9778 !important;
  opacity: 0.7 !important;
}

/* For legacy mat-form-field */
.mat-form-field {
  /* Default border color */
  .mat-form-field-outline {
    color: #fb9778 !important;
  }

  .mat-form-field-outline-thick {
    color: #fb9778 !important;
  }

  &.mat-focused .mat-form-field-outline-thick {
    color: #fb9778 !important;
  }

  &.mat-focused .mat-form-field-label {
    color: #fb9778 !important;
  }

  .mat-input-element::placeholder {
    color: #fb9778 !important;
    opacity: 0.7 !important;
  }
}

/* Additional input styling */
input.mat-mdc-input-element,
textarea.mat-mdc-input-element {
  &::placeholder {
    color: #fb9778 !important;
    opacity: 0.7 !important;
  }

  &:focus {
    caret-color: #fb9778 !important;
  }
}
.activeMenu .mdc-list-item__content{
    color: #fff;
    background-color: #03c9d7;
}
.main-container .mat-mdc-card.mdc-card{
    background: #fff;
    border-radius: 20px;
    box-shadow: 1px 0 20px #00000014 !important;
    padding: 0;
    margin: 15px;
    overflow: hidden;
}
.flex{
  display: flex;
}
