{"name": "church-app-it", "version": "1.0.0", "scripts": {"ng": "ng", "prepare": "husky install", "start": "ng serve", "build:stage": "NODE_ENV=stage ng build --configuration stage", "build:prod": "NODE_ENV=production ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "eslint './src/**/*.ts'", "lint-fix": "npm run lint -- --fix"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/cdk": "^19.2.19", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/flex-layout": "^15.0.0-beta.42", "@angular/forms": "^19.0.0", "@angular/material": "^19.2.19", "@angular/material-moment-adapter": "^19.2.19", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@auth0/angular-jwt": "^5.0.2", "@ngrx/store": "^13.0.2", "angular-calendar": "^0.29.0", "angular-feather": "^6.3.0", "apexcharts": "^3.16.0", "crypto-js": "^4.1.1", "date-fns": "^2.28.0", "moment": "^2.24.0", "ng-apexcharts": "1.5.6", "ng2-search-filter": "^0.5.1", "ngx-pagination": "^5.1.1", "ngx-perfect-scrollbar": "^10.1.1", "ngx-permissions": "^13.0.1", "ngx-scrollbar": "^15.1.3", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.0", "@angular-eslint/builder": "18.4.0", "@angular-eslint/eslint-plugin": "18.4.0", "@angular-eslint/eslint-plugin-template": "18.4.0", "@angular-eslint/schematics": "18.4.0", "@angular-eslint/template-parser": "18.4.0", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@types/crypto-js": "^4.1.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "8.15.0", "@typescript-eslint/parser": "8.15.0", "eslint": "^9.15.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^7.0.4", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "^2.6.2", "prettier-eslint": "^13.0.0", "typescript": "~5.6.0"}}