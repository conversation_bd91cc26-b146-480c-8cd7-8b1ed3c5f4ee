{"cSpell.words": ["cvalue", "darktoggle", "datatable", "horizontaltoggle", "IAPI", "minisidebar", "tithingapps", "Topbar"], "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true}}