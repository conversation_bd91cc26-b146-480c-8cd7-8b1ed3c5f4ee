{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"Flexy-angular": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/Flexy-angular", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/angular-calendar/css/angular-calendar.css", "src/assets/styles/style.scss", "src/styles.scss"], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "10mb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all", "optimization": true}, "stage": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "10mb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}], "outputHashing": "all", "optimization": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"port": 9195, "buildTarget": "Flexy-angular:build"}, "configurations": {"production": {"buildTarget": "Flexy-angular:build:production"}, "development": {"buildTarget": "Flexy-angular:build:development"}}, "defaultConfiguration": "development"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "9ed1f74e-f84e-4d3c-b4d3-be65f24c3c5e"}}